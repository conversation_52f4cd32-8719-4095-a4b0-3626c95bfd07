﻿namespace SimpleBooks.WEB.Controllers.Business.Warehouse
{
    public class ProductCategoryController : BaseBusinessController<
        ProductCategoryModel,
        ProductCategoryModel,
        CreateProductCategoryViewModel,
        UpdateProductCategoryViewModel,
        IndexProductCategoryFormViewModel,
        CreateProductCategoryFormViewModel,
        UpdateProductCategoryFormViewModel>
    {
        private readonly IProductCategoryService _productCategoryService;

        public ProductCategoryController(IProductCategoryService productCategoryService) : base(productCategoryService)
        {
            _productCategoryService = productCategoryService;
        }

        // Custom Index method to provide hierarchical data
        [HttpGet]
        public override async Task<IActionResult> Index(int pageNumber = 1)
        {
            // Get all categories with their relationships
            var allCategories = await _productCategoryService.GetAllAsync();

            // Build hierarchical structure
            var hierarchicalCategories = BuildHierarchicalStructure(allCategories.Data ?? new List<ProductCategoryModel>());

            IndexProductCategoryFormViewModel viewModel = new IndexProductCategoryFormViewModel()
            {
                MainList = new PaginationList<ProductCategoryModel>(
                    hierarchicalCategories,
                    hierarchicalCategories.Count,
                    pageNumber,
                    hierarchicalCategories.Count // Show all in tree view
                ),
                SearchValue = string.Empty,
                PageNumber = pageNumber,
            };

            return View(viewModel);
        }

        [HttpPost]
        public override async Task<IActionResult> Index(IndexProductCategoryFormViewModel model, int pageNumber = 1)
        {
            // Get all categories with their relationships
            var allCategories = await _productCategoryService.GetAllAsync();
            var categoriesList = allCategories.Data ?? new List<ProductCategoryModel>();

            // Filter by search if provided
            if (!string.IsNullOrEmpty(model.SearchValue))
            {
                categoriesList = categoriesList.Where(c =>
                    c.ProductCategoryName.Contains(model.SearchValue, StringComparison.OrdinalIgnoreCase))
                    .ToList();
            }

            // Build hierarchical structure
            var hierarchicalCategories = BuildHierarchicalStructure(categoriesList);

            IndexProductCategoryFormViewModel viewModel = new IndexProductCategoryFormViewModel()
            {
                MainList = new PaginationList<ProductCategoryModel>(
                    hierarchicalCategories,
                    hierarchicalCategories.Count,
                    pageNumber,
                    hierarchicalCategories.Count // Show all in tree view
                ),
                SearchValue = model.SearchValue,
                PageNumber = pageNumber,
            };

            return View(viewModel);
        }

        private List<ProductCategoryModel> BuildHierarchicalStructure(IEnumerable<ProductCategoryModel> categories)
        {
            var categoryList = categories.ToList();
            var rootCategories = new List<ProductCategoryModel>();

            // Get root categories (those without parent)
            var roots = categoryList.Where(c => c.ProductParentCategoryId == null).ToList();

            foreach (var root in roots)
            {
                var hierarchicalRoot = BuildCategoryTree(root, categoryList);
                rootCategories.Add(hierarchicalRoot);
            }

            return rootCategories;
        }

        private ProductCategoryModel BuildCategoryTree(ProductCategoryModel category, List<ProductCategoryModel> allCategories)
        {
            // Create a copy to avoid modifying the original
            var categoryNode = new ProductCategoryModel
            {
                Id = category.Id,
                ProductCategoryName = category.ProductCategoryName,
                ProductParentCategoryId = category.ProductParentCategoryId,
                ProductParentCategory = category.ProductParentCategory,
                ProductSubCategories = new List<ProductCategoryModel>()
            };

            // Find and add children
            var children = allCategories.Where(c => c.ProductParentCategoryId == category.Id).ToList();
            foreach (var child in children)
            {
                var childNode = BuildCategoryTree(child, allCategories);
                categoryNode.ProductSubCategories.Add(childNode);
            }

            return categoryNode;
        }

        public override async Task<IActionResult> Create(CreateProductCategoryFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _productCategoryService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("productCategory", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            ProductCategoryModel? entity = await _productCategoryService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateProductCategoryFormViewModel viewModel = new UpdateProductCategoryFormViewModel()
            {
                Id = entity.Id,
                ProductCategoryName = entity.ProductCategoryName,
                ProductParentCategoryId = entity.ProductParentCategoryId,
                ProductCategories = await _productCategoryService.GetSelectListAsync().ToSelectListItemAsync(),
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateProductCategoryFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _productCategoryService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("productCategory", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<List<CreateProductCategoryViewModel>> HandleImportingEntity(IEnumerable<IXLRangeRow> rows)
        {
            List<CreateProductCategoryViewModel> createEntites = new List<CreateProductCategoryViewModel>();
            var createEntitesDict = new Dictionary<string, CreateProductCategoryViewModel>();

            foreach (var row in rows)
            {
                var mainKey = row.Cell(1).GetString();

                if (!createEntitesDict.TryGetValue(mainKey, out var existingKey))
                {
                    existingKey = new CreateProductCategoryViewModel
                    {
                        ProductCategoryName = row.Cell(1).GetString(),
                    };

                    createEntitesDict[mainKey] = existingKey;
                }
            }

            createEntites = createEntitesDict.Values.ToList();
            return await Task.FromResult(createEntites);
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateProductCategoryFormViewModel model)
        {
            model.ProductCategories = await _productCategoryService.GetSelectListAsync().ToSelectListItemAsync();
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateProductCategoryFormViewModel model)
        {
            model.ProductCategories = await _productCategoryService.GetSelectListAsync().ToSelectListItemAsync();
            return await Task.FromResult(View(model));
        }
    }
}
