﻿namespace SimpleBooks.WEB.Controllers.Business.Warehouse
{
    public class ProductCategoryController : BaseBusinessController<
        ProductCategoryModel,
        ProductCategoryModel,
        CreateProductCategoryViewModel,
        UpdateProductCategoryViewModel,
        IndexProductCategoryFormViewModel,
        CreateProductCategoryFormViewModel,
        UpdateProductCategoryFormViewModel>
    {
        private readonly IProductCategoryService _productCategoryService;

        public ProductCategoryController(IProductCategoryService productCategoryService) : base(productCategoryService)
        {
            _productCategoryService = productCategoryService;
        }

        public override async Task<IActionResult> Create(CreateProductCategoryFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _productCategoryService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("productCategory", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            ProductCategoryModel? entity = await _productCategoryService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateProductCategoryFormViewModel viewModel = new UpdateProductCategoryFormViewModel()
            {
                Id = entity.Id,
                ProductCategoryName = entity.ProductCategoryName,
                ProductParentCategoryId = entity.ProductParentCategoryId,
                ProductCategories = await _productCategoryService.GetSelectListAsync().ToSelectListItemAsync(),
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateProductCategoryFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _productCategoryService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("productCategory", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<List<CreateProductCategoryViewModel>> HandleImportingEntity(IEnumerable<IXLRangeRow> rows)
        {
            List<CreateProductCategoryViewModel> createEntites = new List<CreateProductCategoryViewModel>();
            var createEntitesDict = new Dictionary<string, CreateProductCategoryViewModel>();

            foreach (var row in rows)
            {
                var mainKey = row.Cell(1).GetString();

                if (!createEntitesDict.TryGetValue(mainKey, out var existingKey))
                {
                    existingKey = new CreateProductCategoryViewModel
                    {
                        ProductCategoryName = row.Cell(1).GetString(),
                    };

                    createEntitesDict[mainKey] = existingKey;
                }
            }

            createEntites = createEntitesDict.Values.ToList();
            return await Task.FromResult(createEntites);
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateProductCategoryFormViewModel model)
        {
            model.ProductCategories = await _productCategoryService.GetSelectListAsync().ToSelectListItemAsync();
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateProductCategoryFormViewModel model)
        {
            model.ProductCategories = await _productCategoryService.GetSelectListAsync().ToSelectListItemAsync();
            return await Task.FromResult(View(model));
        }
    }
}
