﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Warehouse
{
    public class ProductCategoryConfiguration : IEntityTypeConfiguration<ProductCategoryModel>
    {
        public void Configure(EntityTypeBuilder<ProductCategoryModel> builder)
        {
            builder.<PERSON><PERSON><PERSON>(x => new { x.Id });

            builder.HasIndex(x => x.ProductCategoryName).IsUnique();

            builder.HasOne(d => d.ProductParentCategory).WithMany(p => p.ProductSubCategories)
                .HasForeignKey(d => d.ProductParentCategoryId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
