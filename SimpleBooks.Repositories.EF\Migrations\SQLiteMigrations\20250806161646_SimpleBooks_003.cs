﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace SimpleBooks.Repositories.EF.Migrations.SQLiteMigrations
{
    /// <inheritdoc />
    public partial class SimpleBooks_003 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "ProductType",
                columns: new[] { "Id", "ProductTypeName" },
                values: new object[,]
                {
                    { "01K200EEQRXXNWAX4PNMKS9Y14", "Invenrory Part" },
                    { "01K200EK5QDX1JJDS2BEET8RRC", "Service" },
                    { "01K200EQ20R4HSGTQPE833K9Q3", "Discount" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "ProductType",
                keyColumn: "Id",
                keyValue: "01K200EEQRXXNWAX4PNMKS9Y14");

            migrationBuilder.DeleteData(
                table: "ProductType",
                keyColumn: "Id",
                keyValue: "01K200EK5QDX1JJDS2BEET8RRC");

            migrationBuilder.DeleteData(
                table: "ProductType",
                keyColumn: "Id",
                keyValue: "01K200EQ20R4HSGTQPE833K9Q3");
        }
    }
}
