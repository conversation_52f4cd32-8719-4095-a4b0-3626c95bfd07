﻿@model CreateProductCategoryFormViewModel

@{
    ViewData["Title"] = "Add Product Category";
}

<h5>
    <i class="bi bi-plus-circle-dotted"></i>
    Add a new Product Category
</h5>

<form asp-controller="ProductCategory" enctype="multipart/form-data">
    <div asp-validation-summary="All" class="text-danger" data-validation-summary="true"></div>
    <div class="row">
        <div class="col-md-6 mt-2">
            <div class="form-group">
                <label asp-for="ProductCategoryName" class="form-label mt-2"></label>
                <input type="text" class="form-control" asp-for="ProductCategoryName" placeholder="Product Category Name">
                <span asp-validation-for="ProductCategoryName" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="ProductParentCategoryId" class="form-label mt-2"></label>
                <select class="form-select" asp-for="ProductParentCategoryId" asp-items="Model.ProductCategories"
                        data-placeholder="Select a parent category" data-minimum-results-for-search="Infinity">
                    <option value=""></option>
                </select>
                <span asp-validation-for="ProductParentCategoryId" class="text-danger"></span>
            </div>
            <button type="submit" class="btn btn-primary mt-4">Save</button>
        </div>
    </div>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}