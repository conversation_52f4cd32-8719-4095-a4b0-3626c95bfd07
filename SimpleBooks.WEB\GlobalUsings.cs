﻿global using ClosedXML.Excel;
global using GMCadiomCore.Models.BaseModels;
global using GMCadiomCore.Models.Enumerations;
global using GMCadiomCore.Models.ResultPattern;
global using GMCadiomCore.Models.ViewModel;
global using GMCadiomCore.Shared.Extensions;
global using GMCadiomCore.Shared.Helper;
global using Microsoft.AspNetCore.Authorization;
global using Microsoft.AspNetCore.Mvc;
global using Microsoft.AspNetCore.Mvc.Filters;
global using Microsoft.AspNetCore.Mvc.ModelBinding;
global using Microsoft.AspNetCore.Mvc.Rendering;
global using Microsoft.AspNetCore.Mvc.ViewFeatures;
global using Microsoft.Extensions.DependencyInjection;
global using Newtonsoft.Json;
global using SimpleBooks.Models.Enumerations;
global using SimpleBooks.Models.JsonConverters;
global using SimpleBooks.Models.Model.HR;
global using SimpleBooks.Models.Model.Purchases;
global using SimpleBooks.Models.Model.Sales;
global using SimpleBooks.Models.Model.Treasury;
global using SimpleBooks.Models.Model.Treasury.BankManagement;
global using SimpleBooks.Models.Model.Treasury.BankManagement.BankTransferManagement;
global using SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement;
global using SimpleBooks.Models.Model.Treasury.CashManagement;
global using SimpleBooks.Models.Model.User;
global using SimpleBooks.Models.Model.Warehouse;
global using SimpleBooks.Models.ModelDTO.Authentication;
global using SimpleBooks.Models.ModelDTO.Purchases;
global using SimpleBooks.Models.ModelDTO.Sales;
global using SimpleBooks.Models.ModelDTO.Tax;
global using SimpleBooks.Models.ModelDTO.Treasury;
global using SimpleBooks.Models.ModelMapper.HR;
global using SimpleBooks.Models.ModelMapper.Purchases;
global using SimpleBooks.Models.ModelMapper.Sales;
global using SimpleBooks.Models.ModelMapper.Tax;
global using SimpleBooks.Models.ModelMapper.Treasury;
global using SimpleBooks.Models.ModelMapper.Treasury.BankManagement;
global using SimpleBooks.Models.ModelMapper.Treasury.BankManagement.BankTransferManagement;
global using SimpleBooks.Models.ModelMapper.Treasury.BankManagement.CheckManagement;
global using SimpleBooks.Models.ModelMapper.Treasury.CashManagement;
global using SimpleBooks.Models.ModelMapper.User;
global using SimpleBooks.Models.ModelMapper.Warehouse;
global using SimpleBooks.Models.ViewModel.BaseModels;
global using SimpleBooks.Models.ViewModel.HR.Employee;
global using SimpleBooks.Models.ViewModel.Purchases.Bill;
global using SimpleBooks.Models.ViewModel.Purchases.BillReturn;
global using SimpleBooks.Models.ViewModel.Purchases.PurchaseOrder;
global using SimpleBooks.Models.ViewModel.Purchases.Vendor;
global using SimpleBooks.Models.ViewModel.Purchases.VendorType;
global using SimpleBooks.Models.ViewModel.Sales.Customer;
global using SimpleBooks.Models.ViewModel.Sales.CustomerType;
global using SimpleBooks.Models.ViewModel.Sales.Invoice;
global using SimpleBooks.Models.ViewModel.Sales.InvoiceReturn;
global using SimpleBooks.Models.ViewModel.Sales.SalesOrder;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.Bank;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.BankAccount;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.BankTransferManagement.BankTransferTreasuryVoucher;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckClear;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckCollection;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckDeposit;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckReject;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckReturn;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckTreasuryVoucher;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckVault;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckVaultLocation;
global using SimpleBooks.Models.ViewModel.Treasury.CashManagement.CashTreasuryVoucher;
global using SimpleBooks.Models.ViewModel.Treasury.CashManagement.Drawer;
global using SimpleBooks.Models.ViewModel.Treasury.CashManagement.DrawerLocation;
global using SimpleBooks.Models.ViewModel.Treasury.Expenses;
global using SimpleBooks.Models.ViewModel.Treasury.PaymentTerm;
global using SimpleBooks.Models.ViewModel.Treasury.TreasuryLine;
global using SimpleBooks.Models.ViewModel.User.ScreensAccessProfile;
global using SimpleBooks.Models.ViewModel.User.Setting;
global using SimpleBooks.Models.ViewModel.User.User;
global using SimpleBooks.Models.ViewModel.Warehouse.Inventory;
global using SimpleBooks.Models.ViewModel.Warehouse.Product;
global using SimpleBooks.Models.ViewModel.Warehouse.ProductCategory;
global using SimpleBooks.Models.ViewModel.Warehouse.ProductTax;
global using SimpleBooks.Models.ViewModel.Warehouse.ProductUnit;
global using SimpleBooks.Models.ViewModel.Warehouse.Store;
global using SimpleBooks.Models.ViewModel.Warehouse.Unit;
global using SimpleBooks.PermissionAndSession.Authentication;
global using SimpleBooks.PermissionAndSession.Authentication.Extensions;
global using SimpleBooks.PermissionAndSession.DI;
global using SimpleBooks.PermissionAndSession.Session;
global using SimpleBooks.Services.API.DI;
global using SimpleBooks.Services.Core.Authentication;
global using SimpleBooks.Services.Core.BaseService;
global using SimpleBooks.Services.Core.Business.HR;
global using SimpleBooks.Services.Core.Business.Purchases;
global using SimpleBooks.Services.Core.Business.Sales;
global using SimpleBooks.Services.Core.Business.Treasury;
global using SimpleBooks.Services.Core.Business.Treasury.BankManagement;
global using SimpleBooks.Services.Core.Business.Treasury.BankManagement.BankTransferManagement;
global using SimpleBooks.Services.Core.Business.Treasury.BankManagement.CheckManagement;
global using SimpleBooks.Services.Core.Business.Treasury.CashManagement;
global using SimpleBooks.Services.Core.Business.User;
global using SimpleBooks.Services.Core.Business.Warehouse;
global using SimpleBooks.Shared.Helper;
global using SimpleBooks.Web.Controllers;
global using SimpleBooks.Web.Helpers;
global using SimpleBooks.Web.Helpers.Extensions;
global using SimpleBooks.Web.Middlewares;
global using SimpleBooks.Web.ViewModels.Base;
global using SimpleBooks.WEB.Attributes;
global using SimpleBooks.WEB.Helpers.Extensions;
global using SimpleBooks.WEB.Models;
global using SimpleBooks.WEB.ViewModels.HR.Employee;
global using SimpleBooks.WEB.ViewModels.Purchases.Bill;
global using SimpleBooks.WEB.ViewModels.Purchases.BillReturn;
global using SimpleBooks.WEB.ViewModels.Purchases.PurchaseOrder;
global using SimpleBooks.WEB.ViewModels.Purchases.Vendor;
global using SimpleBooks.WEB.ViewModels.Purchases.VendorType;
global using SimpleBooks.WEB.ViewModels.Sales.Customer;
global using SimpleBooks.WEB.ViewModels.Sales.CustomerType;
global using SimpleBooks.WEB.ViewModels.Sales.Invoice;
global using SimpleBooks.WEB.ViewModels.Sales.InvoiceReturn;
global using SimpleBooks.WEB.ViewModels.Sales.SalesOrder;
global using SimpleBooks.WEB.ViewModels.Treasury.BankManagement.Bank;
global using SimpleBooks.WEB.ViewModels.Treasury.BankManagement.BankTransferManagement.BankTransferTreasuryVoucher;
global using SimpleBooks.WEB.ViewModels.Treasury.BankManagement.CheckManagement.CheckClear;
global using SimpleBooks.WEB.ViewModels.Treasury.BankManagement.CheckManagement.CheckCollection;
global using SimpleBooks.WEB.ViewModels.Treasury.BankManagement.CheckManagement.CheckDeposit;
global using SimpleBooks.WEB.ViewModels.Treasury.BankManagement.CheckManagement.CheckReject;
global using SimpleBooks.WEB.ViewModels.Treasury.BankManagement.CheckManagement.CheckReturn;
global using SimpleBooks.WEB.ViewModels.Treasury.BankManagement.CheckManagement.CheckTreasuryVoucher;
global using SimpleBooks.WEB.ViewModels.Treasury.BankManagement.CheckManagement.CheckVault;
global using SimpleBooks.WEB.ViewModels.Treasury.CashManagement.CashTreasuryVoucher;
global using SimpleBooks.WEB.ViewModels.Treasury.CashManagement.Drawer;
global using SimpleBooks.WEB.ViewModels.Treasury.Expenses;
global using SimpleBooks.WEB.ViewModels.Treasury.PaymentTerm;
global using SimpleBooks.WEB.ViewModels.User.Setting;
global using SimpleBooks.WEB.ViewModels.User.User;
global using SimpleBooks.WEB.ViewModels.Warehouse.Product;
global using SimpleBooks.WEB.ViewModels.Warehouse.ProductCategory;
global using SimpleBooks.WEB.ViewModels.Warehouse.Store;
global using SimpleBooks.WEB.ViewModels.Warehouse.Unit;
global using System.ComponentModel.DataAnnotations;
global using System.Diagnostics;
global using System.IdentityModel.Tokens.Jwt;
global using System.Reflection;
global using System.Text;
