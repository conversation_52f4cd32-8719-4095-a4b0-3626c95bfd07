﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Warehouse
{
    public class ProductTypeConfiguration : IEntityTypeConfiguration<ProductTypeModel>
    {
        public void Configure(EntityTypeBuilder<ProductTypeModel> builder)
        {
            builder.<PERSON>Key(x => new { x.Id });

            builder.HasIndex(x => x.ProductTypeName).IsUnique();

            builder.HasData(Data.GetProductTypes());
        }
    }
}
