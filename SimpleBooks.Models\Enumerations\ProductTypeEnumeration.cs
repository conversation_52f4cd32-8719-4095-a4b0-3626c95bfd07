﻿namespace SimpleBooks.Models.Enumerations
{
    public class ProductTypeEnumeration : UlidEnumeration<ProductTypeEnumeration>
    {
        public static readonly ProductTypeEnumeration InvenroryPart = new ProductTypeEnumeration(Ulid.Parse("01K200EEQRXXNWAX4PNMKS9Y14"), "Invenrory Part");
        public static readonly ProductTypeEnumeration Service = new ProductTypeEnumeration(Ulid.Parse("01K200EK5QDX1JJDS2BEET8RRC"), "Service");
        public static readonly ProductTypeEnumeration Discount = new ProductTypeEnumeration(Ulid.Parse("01K200EQ20R4HSGTQPE833K9Q3"), "Discount");

        private ProductTypeEnumeration(Ulid key, string value) : base(key, value)
        {
        }

        public static List<ProductTypeEnumeration> ProductTypeEnumerations
        {
            get => new List<ProductTypeEnumeration>
            {
                InvenroryPart, Service, Discount,
            };
        }
    }
}
