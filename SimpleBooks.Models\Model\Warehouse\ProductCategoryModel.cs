﻿namespace SimpleBooks.Models.Model.Warehouse
{
    [Table("ProductCategory")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<ProductCategoryModel>))]
    public class ProductCategoryModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Product Category Name")]
        public string ProductCategoryName { get; set; }

        [DisplayName("Product Parent Category")]
        public Ulid? ProductParentCategoryId { get; set; }
        public virtual ProductCategoryModel? ProductParentCategory { get; set; }

        public virtual ICollection<ProductModel> Products { get; set; } = new List<ProductModel>();
        public virtual ICollection<ProductCategoryModel> ProductSubCategories { get; set; } = new List<ProductCategoryModel>();
    }
}
