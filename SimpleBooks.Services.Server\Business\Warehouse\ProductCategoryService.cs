﻿namespace SimpleBooks.Services.Server.Business.Warehouse
{
    public class ProductCategoryService : SimpleBooksBaseService<ProductCategoryModel, ProductCategoryModel, CreateProductCategoryViewModel, UpdateProductCategoryViewModel>, IProductCategoryService
    {
        private readonly IUnitOfWork _unitOfWork;

        public ProductCategoryService(IAuthenticationValidationService authenticationValidationService, IUnitOfWork unitOfWork) : base(authenticationValidationService, unitOfWork.ProductCategory)
        {
            _unitOfWork = unitOfWork;
        }

        protected override Func<IQueryable<ProductCategoryModel>, IIncludableQueryable<ProductCategoryModel, object>>? Includes =>
            x => x
            .Include(xx => xx.ProductParentCategory)
            .Include(xx => xx.ProductSubCategories);

        public override void ValidateEntity(ProductCategoryModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);
            if (isEdit == false)
            {
                RepositorySpecifications<ProductCategoryModel> repositorySpecifications = new RepositorySpecifications<ProductCategoryModel>()
                {
                    SearchValue = x => x.ProductCategoryName == model.ProductCategoryName,
                    IsTackable = false,
                };
                var isExistingProductCategory = _unitOfWork.ProductCategory.Get(repositorySpecifications);
                if (isExistingProductCategory != null && isExistingProductCategory.Id != model.Id)
                    throw new ValidationException("Product Category with the same Name already exists.");
            }
        }
    }
}
