﻿namespace SimpleBooks.Models.ViewModel.Warehouse.ProductCategory
{
    public class CreateProductCategoryViewModel : BaseCreateViewModel, IEntityMapper<ProductCategoryModel, CreateProductCategoryViewModel>
    {
        [CustomRequired]
        [DisplayName("Product Category Name")]
        public string ProductCategoryName { get; set; }
        [DisplayName("Product Parent Category")]
        public Ulid? ProductParentCategoryId { get; set; }

        public CreateProductCategoryViewModel ToDto(ProductCategoryModel entity) => entity.ToCreateDto();

        public ProductCategoryModel ToEntity() => ProductCategoryMapper.ToEntity(this);
    }
}
