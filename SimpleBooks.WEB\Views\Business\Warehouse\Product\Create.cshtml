﻿@model CreateProductFormViewModel

@{
    ViewData["Title"] = "Add Product";
}

<h5>
    <i class="bi bi-plus-circle-dotted"></i>
    Add a new Product
</h5>

<form asp-controller="Product" enctype="multipart/form-data">
    <div asp-validation-summary="All" class="text-danger" data-validation-summary="true"></div>
    <ul class="nav nav-tabs" role="tablist">
        <li class="nav-item" role="presentation">
            <a class="nav-link active" data-bs-toggle="tab" href="#home" aria-selected="true" role="tab">Base Data</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link" data-bs-toggle="tab" href="#ProductUnits" aria-selected="false" tabindex="-1" role="tab">Product Units</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link" data-bs-toggle="tab" href="#ProductTaxes" aria-selected="false" tabindex="-1" role="tab">Product Taxes</a>
        </li>
    </ul>
    <div id="myTabContent" class="tab-content">
        <div class="tab-pane fade show active" id="home" role="tabpanel">
            <div class="row">
                <div class="col-md-6 mt-2">
                    <div class="form-group">
                        <label asp-for="ProductId" class="form-label mt-2"></label>
                        <input type="text" class="form-control" asp-for="ProductId" placeholder="Product Custom ID">
                        <span asp-validation-for="ProductId" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="ProductName" class="form-label mt-2"></label>
                        <input type="text" class="form-control" asp-for="ProductName" placeholder="Product Name">
                        <span asp-validation-for="ProductName" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="ProductPurchasesDescription" class="form-label mt-2"></label>
                        <input type="text" class="form-control" asp-for="ProductPurchasesDescription" placeholder="Product Purchases Description">
                        <span asp-validation-for="ProductPurchasesDescription" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="ProductSalesDescription" class="form-label mt-2"></label>
                        <input type="text" class="form-control" asp-for="ProductSalesDescription" placeholder="Product Sales Description">
                        <span asp-validation-for="ProductSalesDescription" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="ProductTypeId" class="form-label mt-2"></label>
                        <select class="form-select" asp-for="@Model.ProductTypeId" class="form-control"
                                data-placeholder="Select a product type" data-minimum-results-for-search="Infinity">
                            <option value=""></option>
                            @foreach (var item in Model.ProductTypes)
                            {
                                <option value="@item.Value">@item.Name</option>
                            }
                        </select>
                        <span asp-validation-for="ProductTypeId" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="ProductCategoryId" class="form-label mt-2"></label>
                        <select class="form-select" asp-for="ProductCategoryId" asp-items="Model.ProductCategories"
                                data-placeholder="Select a shift" data-minimum-results-for-search="Infinity">
                            <option value=""></option>
                        </select>
                        <span asp-validation-for="ProductCategoryId" class="text-danger"></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-pane fade" id="ProductUnits" role="tabpanel">
            <table id="ProductUnitsDataGridView" class="table">
                <thead>
                    <tr>
                        <th>ProductUnitId</th>
                        <th>ProductUnitSalesPrice</th>
                        <th>ProductUnitRatio</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    @for (int i = 0; i < Model.ProductUnits.Count; i++)
                    {
                        <tr data-row-index="@i">
                            <td>
                                <select class="form-select" asp-for="@Model.ProductUnits[i].ProductUnitId" name="ProductUnits[@i].ProductUnitId" class="form-control"
                                        data-placeholder="Select a unit" data-minimum-results-for-search="Infinity">
                                    <option value=""></option>
                                    @foreach (var item in Model.SelectiveProductUnits)
                                    {
                                        <option value="@item.Value">@item.Text</option>
                                    }
                                </select>
                                <span asp-validation-for="@Model.ProductUnits[i].ProductUnitId" class="text-danger"></span>
                            </td>
                            <td>
                                <input type="number" asp-for="@Model.ProductUnits[i].ProductUnitSalesPrice" name="ProductUnits[@i].ProductUnitSalesPrice" class="form-control" />
                                <span asp-validation-for="@Model.ProductUnits[i].ProductUnitSalesPrice" class="text-danger"></span>
                            </td>
                            <td>
                                <input type="number" asp-for="@Model.ProductUnits[i].ProductUnitRatio" name="ProductUnits[@i].ProductUnitRatio" class="form-control"/>
                                <span asp-validation-for="@Model.ProductUnits[i].ProductUnitRatio" class="text-danger"></span>
                            </td>
                            <td>
                                <button type="button" class="btn btn-danger" onclick="removeRow(this)">Remove</button>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
            <button type="button" class="btn btn-primary" onclick="handler.addUnitRow()">Add Row</button>
        </div>
        <div class="tab-pane fade" id="ProductTaxes" role="tabpanel">
            <table id="ProductTaxesDataGridView" class="table">
                <thead>
                    <tr>
                        <th>TaxTypeId</th>
                        <th>TaxSubTypeId</th>
                        <th>ProductTaxsRatio</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    @for (int i = 0; i < Model.ProductTaxes.Count; i++)
                    {
                        <tr data-row-index="@i">
                            <td>
                                <select class="form-select" asp-for="@Model.ProductTaxes[i].TaxTypeId" name="ProductTaxes[@i].TaxTypeId" class="form-control" onchange="handler.onTaxTypeChanged(this.value, @i)">
                                    <option value=""></option>
                                    @foreach (var item in Model.TaxTypes)
                                    {
                                        <option value="@item.Id">@item.SelectedText</option>
                                    }
                                </select>
                                <span asp-validation-for="@Model.ProductTaxes[i].TaxTypeId" class="text-danger"></span>
                            </td>
                            <td>
                                <select class="form-select" asp-for="@Model.ProductTaxes[i].TaxSubTypeId" name="ProductTaxes[@i].TaxSubTypeId" class="form-control">
                                    <option value=""></option>
                                </select>
                                <span asp-validation-for="@Model.ProductTaxes[i].TaxSubTypeId" class="text-danger"></span>
                            </td>
                            <td>
                                <input type="number" asp-for="@Model.ProductTaxes[i].ProductTaxsRatio" name="ProductTaxes[@i].ProductTaxsRatio" class="form-control" min="0.00" max="1.00" step="0.01" />
                                <span asp-validation-for="@Model.ProductTaxes[i].ProductTaxsRatio" class="text-danger"></span>
                            </td>
                            <td>
                                <button type="button" class="btn btn-danger" onclick="removeRow(this)">Remove</button>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
            <button type="button" class="btn btn-primary" onclick="handler.addTaxRow()">Add Row</button>
        </div>
    </div>
    <button type="submit" class="btn btn-primary mt-4">Create</button>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
	<script src="~/js/tools.js"></script>
    <script type="module">
        import { ProductHandler } from '/js/business/warehouse/productHandler.js';

        const selectiveProductUnits = @Html.Raw(Json.Serialize(Model.SelectiveProductUnits));
        const taxTypes = @Html.Raw(Json.Serialize(Model.TaxTypes));
        const taxSubTypes = @Html.Raw(Json.Serialize(Model.TaxSubTypes));

        window.handler = new ProductHandler(
            selectiveProductUnits,
            taxTypes,
            taxSubTypes
        );
    </script>
}