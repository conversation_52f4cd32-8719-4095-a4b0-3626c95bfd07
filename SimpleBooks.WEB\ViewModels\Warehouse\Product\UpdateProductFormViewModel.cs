﻿namespace SimpleBooks.WEB.ViewModels.Warehouse.Product
{
    public class UpdateProductFormViewModel : UpdateProductViewModel
    {
        public IEnumerable<ProductTypeEnumeration> ProductTypes { get; set; } = Enumerable.Empty<ProductTypeEnumeration>();
        public IEnumerable<SelectListItem> ProductCategories { get; set; } = Enumerable.Empty<SelectListItem>();
        public IEnumerable<SelectListItem> SelectiveProductUnits { get; set; } = Enumerable.Empty<SelectListItem>();
        public IEnumerable<TaxTypeDto> TaxTypes { get; set; } = new List<TaxTypeDto>();
        public IEnumerable<TaxSubTypeDto> TaxSubTypes { get; set; } = new List<TaxSubTypeDto>();
    }
}
