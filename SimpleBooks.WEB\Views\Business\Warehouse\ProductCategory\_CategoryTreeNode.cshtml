@model ProductCategoryModel

<div class="tree-node" data-category-id="@Model.Id">
    <div class="tree-node-content">
        <div class="tree-node-toggle">
            @if (Model.ProductSubCategories?.Any() == true)
            {
                <i class="bi bi-chevron-right tree-toggle-icon" onclick="toggleNode(this)" title="Expand/Collapse"></i>
            }
            else
            {
                <span class="tree-spacer"></span>
            }
        </div>

        <div class="tree-node-icon">
            @if (Model.ProductSubCategories?.Any() == true)
            {
                <i class="bi bi-folder text-warning"></i>
            }
            else
            {
                <i class="bi bi-tag text-primary"></i>
            }
        </div>

        <div class="tree-node-label" title="@Model.ProductCategoryName">
            @Model.ProductCategoryName
            @if (Model.ProductSubCategories?.Any() == true)
            {
                <small class="text-muted ms-2">(@Model.ProductSubCategories.Count)</small>
            }
        </div>

        <div class="tree-node-actions">
            <a class="btn btn-sm btn-info me-1" asp-action="Update" asp-route-id="@Model.Id" title="Edit Category">
                <i class="bi bi-pencil-fill"></i>
            </a>
            <a href="javascript:;" class="btn btn-sm btn-danger js-delete" data-id="@Model.Id" title="Delete Category">
                <i class="bi bi-trash3"></i>
            </a>
        </div>
    </div>

    @if (Model.ProductSubCategories?.Any() == true)
    {
        <div class="tree-node-children" style="display: none;">
            @foreach (var subCategory in Model.ProductSubCategories.OrderBy(c => c.ProductCategoryName))
            {
                @await Html.PartialAsync("_CategoryTreeNode", subCategory)
            }
        </div>
    }
</div>
