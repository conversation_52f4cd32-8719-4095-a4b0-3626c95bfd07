﻿namespace SimpleBooks.Models.ViewModel.Warehouse.ProductCategory
{
    public class UpdateProductCategoryViewModel : BaseUpdateViewModel, IEntityMapper<ProductCategoryModel, UpdateProductCategoryViewModel>
    {
        [CustomRequired]
        [DisplayName("Product Category Name")]
        public string ProductCategoryName { get; set; }
        [DisplayName("Product Parent Category")]
        public Ulid? ProductParentCategoryId { get; set; }

        public UpdateProductCategoryViewModel ToDto(ProductCategoryModel entity) => entity.ToUpdateDto();

        public ProductCategoryModel ToEntity() => ProductCategoryMapper.ToEntity(this);
    }
}
