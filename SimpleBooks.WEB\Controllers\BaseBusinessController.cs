﻿namespace SimpleBooks.Web.Controllers
{
    [Authorize]
    public class BaseBusinessController<TEntity, TEntityView, TEntityCreate, TEntityUpdate, TEntityIndex, TEntityIndexCreate, TEntityIndexUpdate> : Controller
        where TEntity : BaseIdentityModel
        where TEntityView : BaseIdentityModel
        where TEntityCreate : BaseCreateViewModel, IEntityMapper<TEntity, TEntityCreate>
        where TEntityUpdate : BaseUpdateViewModel, IEntityMapper<TEntity, TEntityUpdate>
        where TEntityIndex : BaseIndexFormViewModel<TEntityView>, new()
        where TEntityIndexCreate : BaseCreateViewModel, IEntityMapper<TEntity, TEntityCreate>, new()
        where TEntityIndexUpdate : BaseUpdateViewModel, IEntityMapper<TEntity, TEntityUpdate>, new()
    {
        protected readonly ISimpleBooksBaseService<TEntity, TEntityView, TEntityCreate, TEntityUpdate> _simpleBooksBaseService;

        public BaseBusinessController(ISimpleBooksBaseService<TEntity, TEntityView, TEntityCreate, TEntityUpdate> SimpleBooksBaseService)
        {
            _simpleBooksBaseService = SimpleBooksBaseService;
        }

        [HttpGet]
        public virtual async Task<IActionResult> Index(int pageNumber = 1)
        {
            var result = await _simpleBooksBaseService.GetAllViewAsync(pageNumber);

            TEntityIndex viewModel = new TEntityIndex()
            {
                MainList = result,
                SearchValue = string.Empty,
                PageNumber = pageNumber,
            };

            return View(viewModel);
        }

        [HttpPost]
        public virtual async Task<IActionResult> Index(TEntityIndex model, int pageNumber = 1)
        {
            PaginationList<TEntityView>? result = null;
            if (string.IsNullOrEmpty(model.SearchValue))
                result = await _simpleBooksBaseService.GetAllViewAsync(pageNumber);
            else
                result = await _simpleBooksBaseService.GetAllViewByNameAsync(model.SearchValue, pageNumber);

            TEntityIndex viewModel = new TEntityIndex()
            {
                MainList = result,
                SearchValue = model.SearchValue,
                PageNumber = pageNumber,
            };

            return View(viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> Create()
        {
            TEntityIndexCreate viewModel = new TEntityIndexCreate();
            return await FullFillViewAsync(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public virtual async Task<IActionResult> Create(TEntityIndexCreate model)
        {
            return await Task.FromResult(RedirectToAction(nameof(Index)));
        }

        [HttpGet]
        public virtual async Task<IActionResult> Update(Ulid id)
        {
            return await Task.FromResult(RedirectToAction(nameof(Index)));
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public virtual async Task<IActionResult> Update(TEntityIndexUpdate model)
        {
            return await Task.FromResult(RedirectToAction(nameof(Index)));
        }

        [HttpDelete]
        public async Task<IActionResult> Remove(Ulid id)
        {
            var isDeleted = await _simpleBooksBaseService.RemoveAsync(id);

            return isDeleted ? Ok() : BadRequest();
        }

        [HttpGet]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _simpleBooksBaseService.GetAllAsync();

            if (result == null || !result.IsSuccess || result.Data == null)
                return NotFound();

            var entitsies = result.Data;

            var stream = Excel.CreateExcelFile(entitsies);

            //Give a Name to your Excel File
            string excelName = $"{typeof(TEntity).GetTableNameValue()}-{DateTime.Now.ToString("yyyyMMdd-HHmmss tt")}.xlsx";
            // 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' is the MIME type for Excel files
            return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", excelName);
        }

        [HttpPost]
        public async Task<IActionResult> ImportFromExcel(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                    return BadRequest("Please upload a valid Excel file.");

                var entities = new List<TEntityCreate>();
                int successCount = 0;
                int errorCount = 0;
                int lineNumber = 0;
                List<string> errors = new List<string>();

                using (var stream = file.OpenReadStream())
                using (var workbook = new XLWorkbook(stream))
                {
                    var worksheet = workbook.Worksheets.First();
                    var rows = worksheet.RangeUsed()?.RowsUsed().Skip(1); // Skip header row
                    if (rows == null)
                        return BadRequest("The Excel file is empty or does not contain valid data.");

                    entities = await HandleImportingEntity(rows);
                }

                foreach (var entity in entities)
                {
                    try
                    {
                        lineNumber += 1;
                        var result = await _simpleBooksBaseService.AddAsync(entity);
                        if (result.IsSuccess)
                            successCount += 1;
                        else
                        {
                            errorCount += 1;
                            errors.Add($"Line Number is {lineNumber} => {result.Message}");
                        }
                    }
                    catch (Exception ex)
                    {
                        errorCount += 1;
                        errors.Add($"Line Number is {lineNumber} => {ex.Message}");
                        continue;
                    }
                }

                TempData.Put("ImportStatus", new ImportResult(successCount, errorCount, errors));
                return await Task.FromResult(RedirectToAction(nameof(Index)));
            }
            catch (Exception ex)
            {
                // Log the exception (not implemented here)
                return BadRequest($"An error occurred while importing the file: {ex.Message}");
            }
        }

        protected virtual async Task<List<TEntityCreate>> HandleImportingEntity(IEnumerable<IXLRangeRow> rows)
        {
            throw new NotImplementedException();
        }

        protected virtual async Task<IActionResult> FullFillViewAsync(TEntityIndexCreate model)
        {
            return await Task.FromResult(View(model));
        }

        protected virtual async Task<IActionResult> FailedActionResultAsync(TEntityIndexUpdate model)
        {
            return await Task.FromResult(View(model));
        }
    }

    public class ImportResult
    {
        public ImportResult(int successCount, int errorCount, List<string> errors)
        {
            SuccessCount = successCount;
            ErrorCount = errorCount;
            Errors = errors;
        }

        public int SuccessCount { get; }
        public int ErrorCount { get; }
        public int TotalCount => SuccessCount + ErrorCount;
        public List<string> Errors { get; } = new List<string>();

        public override string ToString() => $"Success Count = {SuccessCount},\nError Count = {ErrorCount},\nTotal Count = {TotalCount}";
    }
}
