﻿namespace SimpleBooks.Models.ModelMapper.Warehouse
{
    public static class ProductCategoryMapper
    {
        public static CreateProductCategoryViewModel ToCreateDto(this ProductCategoryModel entity)
        {
            CreateProductCategoryViewModel viewModel = new CreateProductCategoryViewModel()
            {
                ProductCategoryName = entity.ProductCategoryName,
                ProductParentCategoryId = entity.ProductParentCategoryId,
            };
            return viewModel;
        }

        public static ProductCategoryModel ToEntity(this CreateProductCategoryViewModel entity)
        {
            ProductCategoryModel model = new ProductCategoryModel()
            {
                ProductCategoryName = entity.ProductCategoryName,
                ProductParentCategoryId = entity.ProductParentCategoryId,
            };
            return model;
        }

        public static UpdateProductCategoryViewModel ToUpdateDto(this ProductCategoryModel entity)
        {
            UpdateProductCategoryViewModel viewModel = new UpdateProductCategoryViewModel()
            {
                Id = entity.Id,
                ProductCategoryName = entity.ProductCategoryName,
                ProductParentCategoryId = entity.ProductParentCategoryId,
            };
            return viewModel;
        }

        public static ProductCategoryModel ToEntity(this UpdateProductCategoryViewModel entity)
        {
            ProductCategoryModel model = new ProductCategoryModel()
            {
                Id = entity.Id,
                ProductCategoryName = entity.ProductCategoryName,
                ProductParentCategoryId = entity.ProductParentCategoryId,
            };
            return model;
        }
    }
}
