﻿namespace SimpleBooks.Services.Core.Business.Warehouse
{
    public interface IProductService : ISimpleBooksBaseService<ProductModel, IndexProductViewModel, CreateProductViewModel, UpdateProductViewModel>
    {
        Task<ServiceResult<IEnumerable<ProductTypeEnumeration>>> SelectiveProductTypeListAsync();
        Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveProductCategoryListAsync();
        Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveUnitListAsync();
        Task<ServiceResult<IEnumerable<TaxTypeDto>>> TaxTypeListAsync();
        Task<ServiceResult<IEnumerable<TaxSubTypeDto>>> TaxSubTypeListAsync();
    }
}
