﻿@model IndexProductCategoryFormViewModel

@{
    ViewData["Title"] = "ProductCategories";
    List<ProductCategoryModel> ProductCategories = Model.MainList.Items;
}

@if (TempData.Get<ImportResult>("ImportStatus") is ImportResult importResult)
    @await Html.PartialAsync("_ImportStatus", importResult)

<div class="row d-flex">
    <div class="me-3 pb-2">
        <a class="btn btn-secondary btn-lg float-start" asp-action="Create" method="get">
            <i class="bi bi-plus-circle-dotted"></i>
            Add Product Category
        </a>
        <a class="btn btn-success btn-lg float-end" asp-action="ExportToExcel">
            <i class="bi bi-file-earmark-spreadsheet"></i>
            Export To Excel
        </a>
        <form asp-action="ImportFromExcel" enctype="multipart/form-data" method="post" id="excelUploadForm" style="display: none;">
            <input type="file" name="file" id="excelFileInput" accept=".xlsx, .xls" />
        </form>

        <a class="btn btn-success btn-lg float-end" href="#" onclick="document.getElementById('excelFileInput').click(); return false;">
            <i class="bi bi-file-earmark-spreadsheet-fill"></i>
            Import From Excel
        </a>

        <!-- Loading overlay -->
        <div id="loadingOverlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(255,255,255,0.7); z-index:9999; text-align:center; padding-top:20%;">
            <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">Importing...</span>
            </div>
            <p class="mt-2 fw-bold">Importing Excel file, please wait...</p>
        </div>
    </div>
    <form class="d-flex align-items-center" asp-action="Index">
        <input class="form-control me-3" type="search" asp-for="SearchValue" placeholder="Search">
        <button class="btn btn-info btn-lg" type="submit">Search</button>
    </form>
</div>

@if (!ProductCategories.Any())
{
    <div class="alert alert-warning mt-5">
        <h4 class="alert-heading">No Product Categories!</h4>
        <p class="mb-0">No Product Categories were added yet.</p>
    </div>
}
else
{
    <div class="tree-view-container">
        <div class="tree-view-header mb-3">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Product Categories Tree View</h5>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-2" onclick="expandAll()">
                        <i class="bi bi-arrows-expand"></i> Expand All
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="collapseAll()">
                        <i class="bi bi-arrows-collapse"></i> Collapse All
                    </button>
                </div>
            </div>
        </div>
        <div class="tree-view">
            @if (ProductCategories.Any())
            {
                @foreach (ProductCategoryModel category in ProductCategories)
                {
                    @await Html.PartialAsync("_CategoryTreeNode", category)
                }
            }
            else
            {
                <div class="text-center text-muted py-4">
                    <i class="bi bi-folder2-open fs-1"></i>
                    <p class="mt-2">No categories found matching your search criteria.</p>
                </div>
            }
        </div>
    </div>
}

@section Styles
{
    <style>
        .tree-view-container {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
        }

        .tree-view {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }

        .tree-node {
            margin: 0;
            padding: 0;
        }

        .tree-node-content {
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
            border-radius: 0.25rem;
            transition: background-color 0.2s;
        }

        .tree-node-content:hover {
            background-color: #f8f9fa;
        }

        .tree-node-toggle {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            margin-right: 0.5rem;
        }

        .tree-toggle-icon {
            transition: transform 0.2s;
            color: #6c757d;
        }

        .tree-toggle-icon.expanded {
            transform: rotate(90deg);
        }

        .tree-spacer {
            width: 16px;
            height: 16px;
        }

        .tree-node-icon {
            margin-right: 0.5rem;
            color: #6c757d;
        }

        .tree-node-label {
            flex: 1;
            font-weight: 500;
            color: #212529;
        }

        .tree-node-actions {
            opacity: 0;
            transition: opacity 0.2s;
        }

        .tree-node-content:hover .tree-node-actions {
            opacity: 1;
        }

        .tree-node-children {
            margin-left: 1.5rem;
            border-left: 1px solid #e9ecef;
            padding-left: 1rem;
        }

        .tree-node-children .tree-node:last-child {
            border-left: none;
        }

        /* Additional styling for better UX */
        .tree-view-header {
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 1rem;
        }

        .tree-node-label {
            user-select: none;
            cursor: default;
        }

        .tree-toggle-icon:hover {
            color: #495057 !important;
            transform: scale(1.1);
        }

        .tree-node-icon i {
            font-size: 1.1em;
        }

        /* Responsive design */
        @@media (max-width: 768px) {
            .tree-node-content {
                flex-wrap: wrap;
            }

            .tree-node-actions {
                opacity: 1;
                margin-top: 0.5rem;
                width: 100%;
                justify-content: flex-start;
            }

            .tree-view-header .d-flex {
                flex-direction: column;
                align-items: flex-start !important;
            }

            .tree-view-header .d-flex > div {
                margin-top: 0.5rem;
            }
        }
    </style>
}

@section Scripts
{
    <script src="~/js/ProductCategory-index.js" asp-append-version="true"></script>
    <script>
        const fileInput = document.getElementById('excelFileInput');
        const form = document.getElementById('excelUploadForm');
        const overlay = document.getElementById('loadingOverlay');

        fileInput.addEventListener('change', function () {
            if (this.files.length > 0) {
                overlay.style.display = 'block'; // Show loading
                form.submit(); // Submit form
            }
        });

        // Tree view functionality
        function toggleNode(toggleIcon) {
            const treeNode = toggleIcon.closest('.tree-node');
            const children = treeNode.querySelector('.tree-node-children');

            if (children) {
                const isExpanded = children.style.display !== 'none';

                if (isExpanded) {
                    children.style.display = 'none';
                    toggleIcon.classList.remove('expanded');
                } else {
                    children.style.display = 'block';
                    toggleIcon.classList.add('expanded');
                }
            }
        }

        // Expand all nodes
        function expandAll() {
            const allToggleIcons = document.querySelectorAll('.tree-toggle-icon');
            allToggleIcons.forEach(icon => {
                const treeNode = icon.closest('.tree-node');
                const children = treeNode.querySelector('.tree-node-children');
                if (children) {
                    children.style.display = 'block';
                    icon.classList.add('expanded');
                }
            });
        }

        // Collapse all nodes
        function collapseAll() {
            const allToggleIcons = document.querySelectorAll('.tree-toggle-icon');
            allToggleIcons.forEach(icon => {
                const treeNode = icon.closest('.tree-node');
                const children = treeNode.querySelector('.tree-node-children');
                if (children) {
                    children.style.display = 'none';
                    icon.classList.remove('expanded');
                }
            });
        }

        // Initialize tree view
        document.addEventListener('DOMContentLoaded', function() {
            // You can uncomment the next line if you want all nodes expanded by default
            // expandAll();
        });
    </script>
}