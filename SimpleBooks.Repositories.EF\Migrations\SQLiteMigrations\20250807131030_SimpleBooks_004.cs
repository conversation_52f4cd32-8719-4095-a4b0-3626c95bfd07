﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SimpleBooks.Repositories.EF.Migrations.SQLiteMigrations
{
    /// <inheritdoc />
    public partial class SimpleBooks_004 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ProductParentCategoryId",
                table: "ProductCategory",
                type: "TEXT",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_ProductCategory_ProductParentCategoryId",
                table: "ProductCategory",
                column: "ProductParentCategoryId");

            migrationBuilder.AddForeignKey(
                name: "FK_ProductCategory_ProductCategory_ProductParentCategoryId",
                table: "ProductCategory",
                column: "ProductParentCategoryId",
                principalTable: "ProductCategory",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ProductCategory_ProductCategory_ProductParentCategoryId",
                table: "ProductCategory");

            migrationBuilder.DropIndex(
                name: "IX_ProductCategory_ProductParentCategoryId",
                table: "ProductCategory");

            migrationBuilder.DropColumn(
                name: "ProductParentCategoryId",
                table: "ProductCategory");
        }
    }
}
